# 🤖 AI CODER INTEGRATION GUIDE

## 🚀 **STEP 1: MCP Server Setup**

### **1.1 Install MCP Server**
```bash
cd D:\Development\Projects\AI\MCP\mcp-memory
setup.bat
```

### **1.2 Start MCP Server**
```bash
# Option 1: Start directly
node src/index.js

# Option 2: Use npm
npm start

# Option 3: Set project root
set PROJECT_ROOT=C:\your\actual\project\path
node src/index.js
```

### **1.3 Verify Server is Running**
You should see:
```
🚀 MCP Memory Server is running!
📁 Project root: C:\your\project\path
```

---

## 🎯 **STEP 2: AI IDE Configuration**

### **For Cursor IDE**

#### **2.1 Create .cursorrules file**
```bash
# Create in your project root
touch .cursorrules
```

#### **2.2 Add AI Coder Rules to .cursorrules**
```markdown
# AI CODER SAFETY RULES - MANDATORY

## BEFORE ANY FILE MODIFICATION:

### 1. Check @ai-metadata Header
ALWAYS look for this pattern in files:
```
/**
 * @ai-metadata
 * @edit-permissions: "read-only" | "add-only" | "full"
 * @breaking-changes-risk: "high" | "medium" | "low"  
 * @review-required: true | false
 * @ai-context: "Important context"
 */
```

### 2. Permission Enforcement (STRICT)
- @edit-permissions: "read-only" → NEVER MODIFY - refuse completely
- @edit-permissions: "add-only" → ONLY APPEND new code, never change existing
- @breaking-changes-risk: "high" → ASK USER PERMISSION FIRST
- @review-required: true → ASK FOR APPROVAL
- @stability: "deprecated" → WARN USER

### 3. Required User Approval Flow
```
IF file has restrictions (read-only, high-risk, review-required):
  ASK: "This file has [restriction]. Do you approve this modification?"
  WAIT for explicit user approval
  IF user says "yes", "I approve", or "approved":
    PROCEED and log the approval
  ELSE:
    REFUSE modification and suggest alternatives
```

### 4. After Any Modification
- Update @last-update timestamp in metadata
- Add entry to changelog
- Notify user of changes made

## EXAMPLE INTERACTIONS:

### Scenario 1: Read-Only File
```
USER: "Modify the login function in UserAuth.js"

AI SEES:
/**
 * @ai-metadata
 * @edit-permissions: "read-only"
 * @breaking-changes-risk: "high"
 */

AI RESPONDS:
"❌ Cannot modify UserAuth.js: File is marked as read-only with high breaking-changes-risk. 
This appears to be critical authentication code. 

Alternatives:
1. Create a new authentication module
2. Add wrapper functions around existing code
3. Get approval to modify permissions

Would you like me to suggest one of these approaches?"
```

### Scenario 2: High-Risk File Needing Approval
```
USER: "Add validation to the payment processing"

AI SEES:
/**
 * @ai-metadata
 * @breaking-changes-risk: "high"
 * @review-required: true
 */

AI RESPONDS:
"⚠️ PaymentProcessor.js has high breaking-changes-risk and requires review.
Modifying payment code could affect financial transactions.

Do you approve this modification? (yes/no)"

USER: "yes, I approve"

AI RESPONDS:
"✅ Approval received. I'll add the validation and update the changelog."
[Proceeds with modification]
```

## ERROR MESSAGES TO USE:
❌ "Cannot modify: Read-only permissions"
❌ "High-risk file requires approval first"  
⚠️ "File is deprecated - consider alternatives"
⚠️ "Review required before modification"
✅ "Modification approved and logged"
```

### **For Windsurf**

#### **2.3 Create windsurf.config.js**
```javascript
module.exports = {
  aiSafety: {
    enforceMetadata: true,
    requireApproval: ['read-only', 'high-risk'],
    autoChangelog: true,
    mcpServer: 'mcp-memory-server'
  },
  rules: [
    "Check @ai-metadata before file modification",
    "Respect edit-permissions strictly", 
    "Ask approval for high-risk files",
    "Never modify read-only files",
    "Update changelog after changes"
  ]
};
```

### **For Claude.ai Desktop**

#### **2.4 System Prompt Addition**
```
CRITICAL AI SAFETY RULES:

You are an AI coding assistant with mandatory file modification controls.

BEFORE ANY FILE MODIFICATION:
1. Check for @ai-metadata header in the file
2. Examine @edit-permissions and @breaking-changes-risk
3. If "read-only" → REFUSE modification completely
4. If "high" risk → ASK user permission first
5. If user approves → proceed and update @last-update

NEVER bypass these safety checks. File protection is MANDATORY.

Example metadata to look for:
/**
 * @ai-metadata
 * @edit-permissions: "read-only"
 * @breaking-changes-risk: "high"
 * @ai-context: "Critical system file"
 */

Always respond with appropriate warnings and ask for approval when needed.
```

---

## 📝 **STEP 3: Add Metadata to Your Files**

### **3.1 High-Priority Files (Add These Headers)**

#### **Critical Auth File:**
```javascript
/**
 * @ai-metadata
 * @class: UserAuthentication
 * @description: Core user authentication and security
 * @last-update: 2024-08-22T10:00:00Z
 * @last-editor: <EMAIL>
 * @stability: stable
 * @edit-permissions: read-only
 * @breaking-changes-risk: high
 * @review-required: true
 * @ai-context: "Critical security code - login/auth methods cannot be modified without security review"
 */

// Your authentication code here
```

#### **Payment Processing:**
```javascript
/**
 * @ai-metadata
 * @class: PaymentProcessor
 * @description: Handles payment transactions and billing
 * @last-update: 2024-08-22T10:00:00Z
 * @edit-permissions: method-specific
 * @method-permissions: { "processPayment": "read-only", "validateCard": "allow", "addPaymentMethod": "allow" }
 * @breaking-changes-risk: high
 * @review-required: true
 * @ai-context: "Financial code - processPayment method is critical and cannot be modified"
 */

// Your payment code here
```

#### **Database/API Core:**
```javascript
/**
 * @ai-metadata
 * @class: DatabaseManager
 * @description: Core database operations and connections
 * @edit-permissions: add-only
 * @breaking-changes-risk: high
 * @stability: stable
 * @ai-context: "Core database layer - only add new methods, do not modify existing connections"
 */

// Your database code here
```

#### **Development/Utility Files:**
```javascript
/**
 * @ai-metadata
 * @class: UtilityHelpers
 * @description: General utility functions
 * @edit-permissions: full
 * @breaking-changes-risk: low
 * @stability: experimental
 * @ai-context: "Utility functions - safe to modify and refactor"
 */

// Your utility code here
```

### **3.2 Quick Setup Script**

Create `add-metadata.bat`:
```batch
@echo off
echo Adding @ai-metadata headers to important files...

REM Add to your critical files
echo Adding metadata to auth files...
REM You'll manually add headers to your specific files

echo.
echo ✅ Remember to add @ai-metadata headers to:
echo - Authentication/security files (read-only)
echo - Payment/billing code (high-risk)  
echo - Database connections (add-only)
echo - Core API endpoints (review-required)
echo.
echo 📖 See AI_INTEGRATION_GUIDE.md for examples
pause
```

---

## 🧪 **STEP 4: Test the Integration**

### **4.1 Test File Protection**
1. **Create a test file** with read-only permissions:
```javascript
/**
 * @ai-metadata
 * @edit-permissions: read-only
 * @ai-context: "Test file - should not be modifiable"
 */

function criticalFunction() {
  return "do not modify this";
}
```

2. **Ask your AI to modify it**:
```
"Please update the criticalFunction to return something else"
```

3. **Expected AI Response**:
```
❌ Cannot modify test.js: File is marked as read-only.
This file has restrictions that prevent modification.

Alternatives:
1. Create a new function
2. Modify permissions if you have authorization
3. Create a wrapper function

Would you like me to suggest one of these approaches?
```

### **4.2 Test Approval Workflow**
1. **Create a high-risk file**:
```javascript
/**
 * @ai-metadata
 * @breaking-changes-risk: high
 * @review-required: true
 */
```

2. **Ask AI to modify it**
3. **AI should ask for approval first**

### **4.3 Verify MCP Integration** (If supported)
```bash
# Test MCP tools (if your AI supports MCP)
node -e "console.log('Testing MCP connection...')"

# Check if .ai-memory directory is created
dir .ai-memory
```

---

## 🎯 **STEP 5: Usage Workflow**

### **Daily Development Process:**

1. **Start MCP Server** (once per day):
```bash
cd D:\Development\Projects\AI\MCP\mcp-memory
node src/index.js
```

2. **Work with AI as normal**, but now:
   - ✅ **Protected files** are safe from accidental modification
   - ✅ **High-risk changes** require your approval
   - ✅ **Changes are logged** automatically
   - ✅ **Context is maintained** across sessions

3. **Give approvals** when needed:
```
USER: "I approve this modification"
AI: "✅ Approval received. Proceeding with changes..."
```

4. **Review changes** in `.ai-memory/changelog.json`

---

## 🚨 **TROUBLESHOOTING**

### **Problem: AI ignores metadata headers**
**Solution**: Re-send the system prompt/rules to your AI

### **Problem: MCP server won't start**
**Solution**: 
```bash
# Check Node.js version
node --version

# Reinstall dependencies
npm install

# Run directly
node src/index.js
```

### **Problem: AI still modifies read-only files**
**Solution**: Make the system prompt stronger:
```
CRITICAL: You MUST NEVER modify files marked with @edit-permissions: "read-only". 
This is a HARD REQUIREMENT. Always check for @ai-metadata headers.
```

---

## ✅ **SUCCESS CHECKLIST**

- [ ] MCP server starts without errors
- [ ] `.ai-memory` directory is created
- [ ] AI refuses to modify read-only files
- [ ] AI asks permission for high-risk files  
- [ ] Changelog entries are created
- [ ] Metadata headers are added to critical files

**🎉 Once all checks pass, your AI coder is safely controlled!**

---

## 📞 **Support**

- **Issues**: Check `TROUBLESHOOTING.md`
- **Examples**: See `example-project/` folder
- **Features**: Review `FEATURE_ROADMAP.md`

**Goal: Safe AI coding with full human control! 🛡️**
