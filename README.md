# MCP Memory Server

A comprehensive Model Context Protocol (MCP) server for AI coder memory management, file approvals, and changelog tracking.

## Features

- **Session Memory Management**: Track AI coding sessions, tasks, and decisions
- **File Approval System**: Control which files can be modified based on approval status
- **AI Metadata Headers**: Parse and manage @ai-metadata headers in code files
- **Rule Engine**: Enforce modification rules based on file metadata and approvals
- **Automatic Changelog**: Generate and maintain project changelogs
- **TypeScript**: Fully typed for better development experience

## Installation

```bash
cd D:\Development\Projects\AI\MCP\mcp-memory
npm install
npm run build
```

## Usage

### Start the MCP Server

```bash
npm start
```

### Available Tools

#### Memory Management
- `start_session` - Start a new AI coding session
- `add_session_step` - Record completion of a step
- `add_decision` - Record important technical decisions
- `get_project_memory` - Get current project state

#### Approval Management
- `set_file_approval` - Set approval status for files
- `get_file_approval_status` - Check file approval status

#### Rule Engine
- `check_before_modification` - Check if file can be modified
- `get_modification_actions` - Get required actions after modification

#### Metadata Management
- `parse_file_metadata` - Parse @ai-metadata from files
- `update_file_metadata` - Update file metadata
- `find_files_with_metadata` - Find all files with metadata

#### Changelog Management
- `add_changelog_entry` - Add changelog entries
- `get_file_changelog` - Get changelog for specific file
- `get_recent_changes` - Get recent changes

## Configuration

Set the `PROJECT_ROOT` environment variable to specify the project directory:

```bash
export PROJECT_ROOT=/path/to/your/project
npm start
```

## AI Metadata Header Format

```javascript
/**
 * @ai-metadata
 * @class: YourClassName
 * @description: Brief description of the file
 * @last-update: 2024-08-21T10:30:00Z
 * @last-editor: <EMAIL>
 * @changelog: ./docs/changelog/file-changelog.md
 * @stability: stable | experimental | deprecated
 * @edit-permissions: full | add-only | read-only | method-specific
 * @method-permissions: { "method1": "read-only", "method2": "allow" }
 * @dependencies: ["file1.js", "file2.js"]
 * @tests: ["./tests/file.test.js"]
 * @breaking-changes-risk: high | medium | low
 * @review-required: true | false
 * @ai-context: "Important context about this file"
 * 
 * @approvals:
 *   - dev-approved: true | false
 *   - dev-approved-by: "<EMAIL>"
 *   - dev-approved-date: "2024-08-21T10:30:00Z"
 *   - code-review-approved: true | false
 *   - code-review-approved-by: "<EMAIL>"
 *   - code-review-date: "2024-08-21T11:15:00Z"
 *   - qa-approved: true | false
 *   - qa-approved-by: "<EMAIL>"
 *   - qa-approved-date: "2024-08-21T14:20:00Z"
 */
```

## Development

```bash
# Development mode with auto-reload
npm run dev

# Build TypeScript
npm run build

# Watch mode
npm run watch
```

## File Structure

```
mcp-memory/
├── src/
│   ├── index.ts              # Main MCP server
│   ├── types.ts              # TypeScript interfaces
│   ├── memory-manager.ts     # Session and memory management
│   ├── changelog-manager.ts  # Changelog handling
│   ├── metadata-parser.ts    # Parse @ai-metadata headers
│   └── rule-engine.ts        # Approval and modification rules
├── package.json
├── tsconfig.json
└── README.md
```
