import { ChangelogEntry } from './types.js';
export declare class ChangelogManager {
    private projectRoot;
    private changelogPath;
    private markdownChangelogPath;
    constructor(projectRoot: string);
    addChangelogEntry(entry: Omit<ChangelogEntry, 'date'>): Promise<void>;
    getChangelog(): Promise<ChangelogEntry[]>;
    getChangelogForFile(filePath: string): Promise<ChangelogEntry[]>;
    getRecentChanges(days?: number): Promise<ChangelogEntry[]>;
    private updateMarkdownChangelog;
    private groupChangelogByDate;
    private groupChangelogByType;
    private capitalizeFirst;
    addQuickEntry(description: string, filesChanged: string[], type?: ChangelogEntry['type'], sessionId?: string): Promise<void>;
}
//# sourceMappingURL=changelog-manager.d.ts.map