{"version": 3, "file": "changelog-manager.js", "sourceRoot": "", "sources": ["../src/changelog-manager.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,UAAU,CAAC;AAC/B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,MAAM,OAAO,CAAC;AAG1B,MAAM,OAAO,gBAAgB;IAK3B,YAAY,WAAmB;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC/E,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAmC;QACzD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAE5C,MAAM,QAAQ,GAAmB;gBAC/B,GAAG,KAAK;gBACR,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aAC/B,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,mBAAmB;YAEhD,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;YACjE,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAE9C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,6BAA6B,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5C,OAAO,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC/C,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,0BAA0B,CAAC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QACxC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAE/D,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC9B,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,CACvD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAAe,CAAC;QACrC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC5C,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAEhD,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,CAClC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,SAA2B;QAC/D,IAAI,CAAC;YACH,IAAI,QAAQ,GAAG,iBAAiB,CAAC;YACjC,QAAQ,IAAI,0EAA0E,CAAC;YAEvF,gBAAgB;YAChB,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAE3D,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5D,QAAQ,IAAI,OAAO,IAAI,OAAO,CAAC;gBAE/B,gBAAgB;gBAChB,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBAEzD,KAAK,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;oBAChE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC3B,QAAQ,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;wBAElD,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;4BAChC,QAAQ,IAAI,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC;4BAErC,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;gCACzB,QAAQ,IAAI,wBAAwB,CAAC;4BACvC,CAAC;4BAED,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCAClC,QAAQ,IAAI,KAAK,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;4BACpD,CAAC;4BAED,QAAQ,IAAI,IAAI,CAAC;wBACnB,CAAC;wBACD,QAAQ,IAAI,IAAI,CAAC;oBACnB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;YACzD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,oCAAoC,CAAC,EAAE,KAAK,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,SAA2B;QACtD,MAAM,OAAO,GAAqC,EAAE,CAAC;QAErD,KAAK,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;YAC9B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB;YAC7D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnB,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACrB,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,oBAAoB,CAAC,OAAyB;QACpD,MAAM,OAAO,GAAqC;YAChD,KAAK,EAAE,EAAE;YACT,OAAO,EAAE,EAAE;YACX,UAAU,EAAE,EAAE;YACd,OAAO,EAAE,EAAE;YACX,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,eAAe,CAAC,GAAW;QACjC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,WAAmB,EACnB,YAAsB,EACtB,OAA+B,SAAS,EACxC,SAAkB;QAElB,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAC3B,SAAS,EAAE,SAAS,IAAI,QAAQ;YAChC,IAAI,EAAE,cAAc;YACpB,IAAI;YACJ,WAAW;YACX,YAAY;YACZ,cAAc,EAAE,KAAK;YACrB,SAAS,EAAE,EAAE;YACb,MAAM,EAAE,OAAO;SAChB,CAAC,CAAC;IACL,CAAC;CACF"}