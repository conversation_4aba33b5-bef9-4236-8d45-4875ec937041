import { ProjectMemory, ApprovalStatus } from './types.js';
export declare class MemoryManager {
    private projectRoot;
    private memoryDir;
    private projectMemoryPath;
    private changelogPath;
    private approvalsPath;
    constructor(projectRoot: string);
    private ensureMemoryDir;
    getProjectMemory(): Promise<ProjectMemory>;
    private createDefaultProjectMemory;
    saveProjectMemory(memory: ProjectMemory): Promise<void>;
    startNewSession(task: string): Promise<string>;
    addSessionStep(step: string, filesModified: string[], description?: string): Promise<void>;
    addImportantDecision(key: string, value: any, reasoning: string): Promise<void>;
    setFileApproval(filePath: string, approvalType: keyof ApprovalStatus, approvedBy: string): Promise<void>;
    getFileApprovalStatus(filePath: string): Promise<ApprovalStatus | null>;
    invalidateFileApprovals(filePath: string, reason?: string): Promise<void>;
    private generateSessionId;
    private generateId;
}
//# sourceMappingURL=memory-manager.d.ts.map