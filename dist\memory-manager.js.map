{"version": 3, "file": "memory-manager.js", "sourceRoot": "", "sources": ["../src/memory-manager.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,UAAU,CAAC;AAC/B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAE7B,OAAO,KAAK,MAAM,OAAO,CAAC;AAW1B,MAAM,OAAO,aAAa;IAOxB,YAAY,WAAmB;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QACtD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;QAC1E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;QACpE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,sBAAsB,CAAC,CAAC;QAEvE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrC,CAAC;IAED,4BAA4B;IAC5B,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAChD,OAAO,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACnD,CAAC;YACD,OAAO,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAC3C,CAAC;IACH,CAAC;IAEO,0BAA0B;QAChC,OAAO;YACL,cAAc,EAAE;gBACd,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;gBACrC,YAAY,EAAE,SAAS;gBACvB,SAAS,EAAE,EAAE;gBACb,eAAe,EAAE,4BAA4B;gBAC7C,UAAU,EAAE,MAAM;aACnB;YACD,cAAc,EAAE;gBACd,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBACnC,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACjC,cAAc,EAAE,EAAE;gBAClB,SAAS,EAAE,EAAE;gBACb,kBAAkB,EAAE,EAAE;gBACtB,QAAQ,EAAE,EAAE;aACb;YACD,WAAW,EAAE,EAAE;YACf,eAAe,EAAE,EAAE;YACnB,cAAc,EAAE,EAAE;SACnB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAqB;QAC3C,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;YAClE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,8BAA8B,CAAC,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED,qBAAqB;IACrB,KAAK,CAAC,eAAe,CAAC,IAAY;QAChC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE3C,MAAM,CAAC,cAAc,GAAG;YACtB,SAAS;YACT,IAAI;YACJ,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACjC,cAAc,EAAE,EAAE;YAClB,SAAS,EAAE,EAAE;YACb,kBAAkB,EAAE,EAAE;YACtB,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,2BAA2B,SAAS,EAAE,CAAC,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,CAAC;QAC5C,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,IAAY,EAAE,aAAuB,EAAE,WAAoB;QAC9E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE7C,MAAM,WAAW,GAAgB;YAC/B,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,aAAa;YACb,WAAW;YACX,SAAS,EAAE,CAAC,CAAC,gCAAgC;SAC9C,CAAC;QAEF,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACvD,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAErC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,GAAW,EAAE,KAAU,EAAE,SAAiB;QACnE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE7C,MAAM,CAAC,cAAc,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAEtD,MAAM,QAAQ,GAAa;YACzB,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,QAAQ,EAAE,GAAG,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC5C,SAAS;YACT,MAAM,EAAE,EAAE;YACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,UAAU,EAAE,QAAQ;YACpB,YAAY,EAAE,EAAE;SACjB,CAAC;QAEF,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAErC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,yBAAyB,GAAG,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC;IACvE,CAAC;IAED,2BAA2B;IAC3B,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,YAAkC,EAAE,UAAkB;QAC5F,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAE/D,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC;YACzC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;QAC3C,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAEtD,0BAA0B;QAC1B,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,aAAa;gBAChB,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;gBAC7B,SAAS,CAAC,aAAa,GAAG,UAAU,CAAC;gBACrC,SAAS,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;gBACrD,MAAM;YACR,KAAK,oBAAoB;gBACvB,SAAS,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBACpC,SAAS,CAAC,oBAAoB,GAAG,UAAU,CAAC;gBAC5C,SAAS,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;gBACpD,MAAM;YACR,KAAK,YAAY;gBACf,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC;gBAC5B,SAAS,CAAC,YAAY,GAAG,UAAU,CAAC;gBACpC,SAAS,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;gBACpD,MAAM;QACV,CAAC;QAED,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAErC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,YAAY,YAAY,YAAY,OAAO,UAAU,EAAE,CAAC,CAAC,CAAC;IACzF,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC/D,OAAO,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,QAAgB,EAAE,SAAiB,eAAe;QAC9E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAE/D,IAAI,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC;YACxC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG;gBACpC,WAAW,EAAE,KAAK;gBAClB,kBAAkB,EAAE,KAAK;gBACzB,UAAU,EAAE,KAAK;aAClB,CAAC;YAEF,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,iCAAiC,YAAY,KAAK,MAAM,EAAE,CAAC,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAEO,iBAAiB;QACvB,OAAO,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC5E,CAAC;IAEO,UAAU;QAChB,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACpE,CAAC;CACF"}