import { AIMetadata } from './types.js';
export declare class MetadataParser {
    private projectRoot;
    constructor(projectRoot: string);
    parseFileMetadata(filePath: string): Promise<AIMetadata | null>;
    extractMetadataFromContent(content: string): AIMetadata | null;
    private parseField;
    private parseArrayField;
    private parseJson<PERSON>ield;
    private parseApprovals;
    updateFileMetadata(filePath: string, updates: Partial<AIMetadata>): Promise<void>;
    updateMetadataInContent(content: string, updates: Partial<AIMetadata>): string;
    private updateMetadataField;
    private generateMetadataBlock;
    findFilesWithMetadata(pattern?: string): Promise<string[]>;
}
//# sourceMappingURL=metadata-parser.d.ts.map