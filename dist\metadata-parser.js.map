{"version": 3, "file": "metadata-parser.js", "sourceRoot": "", "sources": ["../src/metadata-parser.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,UAAU,CAAC;AAE/B,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,KAAK,MAAM,OAAO,CAAC;AAG1B,MAAM,OAAO,cAAc;IAGzB,YAAY,WAAmB;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QACtC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,sBAAsB,QAAQ,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,0BAA0B,CAAC,OAAe;QACxC,8BAA8B;QAC9B,MAAM,aAAa,GAAG,wCAAwC,CAAC;QAC/D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAE3C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,QAAQ,GAAe,EAAE,CAAC;QAEhC,mBAAmB;QACnB,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAC7E,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,eAAe,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;QACzF,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,eAAe,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;QACxF,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,eAAe,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;QACxF,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;QACrF,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAY,CAAC,CAAC;QAC5F,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,oBAAoB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,eAAe,GAAG,KAAY,CAAC,CAAC;QACzG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,yBAAyB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,mBAAmB,GAAG,KAAY,CAAC,CAAC;QAClH,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,KAAK,MAAM,CAAC,CAAC;QAC3G,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,cAAc,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;QAEtF,eAAe;QACf,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,gBAAgB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;QAChG,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAElF,yCAAyC;QACzC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,sBAAsB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,iBAAiB,GAAG,KAAK,CAAC,CAAC;QAE1G,kBAAkB;QAClB,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAExD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,UAAU,CAAC,OAAe,EAAE,KAAa,EAAE,MAA+B;QAChF,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;QACjG,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,OAAe,EAAE,KAAa,EAAE,MAAiC;QACvF,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;QACpG,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAChF,MAAM,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,OAAe,EAAE,KAAa,EAAE,MAA4B;QACjF,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;QAC9F,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,MAAM,SAAS,GAAQ,EAAE,CAAC;QAE1B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,gBAAgB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,GAAG,KAAK,KAAK,MAAM,CAAC,CAAC;QAChG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,qBAAqB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,eAAe,GAAG,KAAK,CAAC,CAAC;QAC9F,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,wBAAwB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,kBAAkB,GAAG,KAAK,KAAK,MAAM,CAAC,CAAC;QAC/G,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,2BAA2B,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAC;QACzG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,oBAAoB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC;QAC5F,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,GAAG,KAAK,KAAK,MAAM,CAAC,CAAC;QAC9F,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,kBAAkB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;QACxF,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,oBAAoB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC;QAE5F,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;IACnE,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE,OAA4B;QACrE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACrD,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACtE,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,8BAA8B,QAAQ,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED,uBAAuB,CAAC,OAAe,EAAE,OAA4B;QACnE,MAAM,aAAa,GAAG,wCAAwC,CAAC;QAC/D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAE3C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,mCAAmC;YACnC,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACxD,OAAO,WAAW,GAAG,IAAI,GAAG,OAAO,CAAC;QACtC,CAAC;QAED,2BAA2B;QAC3B,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAE7B,mBAAmB;QACnB,OAAO,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAE9C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACnD,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IACvD,CAAC;IAEO,mBAAmB,CAAC,aAAqB,EAAE,GAAW,EAAE,KAAU;QACxE,MAAM,QAAQ,GAA2B;YACvC,UAAU,EAAE,eAAe;YAC3B,UAAU,EAAE,eAAe;YAC3B,eAAe,EAAE,oBAAoB;YACrC,mBAAmB,EAAE,yBAAyB;YAC9C,cAAc,EAAE,mBAAmB;SACpC,CAAC;QAEF,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK;YAAE,OAAO,aAAa,CAAC;QAEjC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QACjG,MAAM,WAAW,GAAG,GAAG,KAAK,IAAI,KAAK,EAAE,CAAC;QAExC,IAAI,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,sCAAsC;YACtC,OAAO,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,WAAW,OAAO,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,QAA6B;QACzD,IAAI,KAAK,GAAG,wBAAwB,CAAC;QAErC,IAAI,QAAQ,CAAC,KAAK;YAAE,KAAK,IAAI,cAAc,QAAQ,CAAC,KAAK,IAAI,CAAC;QAC9D,IAAI,QAAQ,CAAC,WAAW;YAAE,KAAK,IAAI,oBAAoB,QAAQ,CAAC,WAAW,IAAI,CAAC;QAChF,KAAK,IAAI,oBAAoB,QAAQ,CAAC,UAAU,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC;QACjF,IAAI,QAAQ,CAAC,UAAU;YAAE,KAAK,IAAI,oBAAoB,QAAQ,CAAC,UAAU,IAAI,CAAC;QAC9E,IAAI,QAAQ,CAAC,SAAS;YAAE,KAAK,IAAI,kBAAkB,QAAQ,CAAC,SAAS,IAAI,CAAC;QAC1E,IAAI,QAAQ,CAAC,eAAe;YAAE,KAAK,IAAI,yBAAyB,QAAQ,CAAC,eAAe,IAAI,CAAC;QAC7F,IAAI,QAAQ,CAAC,mBAAmB;YAAE,KAAK,IAAI,8BAA8B,QAAQ,CAAC,mBAAmB,IAAI,CAAC;QAC1G,IAAI,QAAQ,CAAC,cAAc,KAAK,SAAS;YAAE,KAAK,IAAI,wBAAwB,QAAQ,CAAC,cAAc,IAAI,CAAC;QACxG,IAAI,QAAQ,CAAC,SAAS;YAAE,KAAK,IAAI,mBAAmB,QAAQ,CAAC,SAAS,IAAI,CAAC;QAE3E,KAAK,IAAI,KAAK,CAAC;QACf,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,UAAkB,sCAAsC;QAClF,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;gBAChC,GAAG,EAAE,IAAI,CAAC,WAAW;gBACrB,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,CAAC,oBAAoB,EAAE,YAAY,EAAE,YAAY,CAAC;aAC3D,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAa,EAAE,CAAC;YAEvC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACpD,IAAI,QAAQ,EAAE,CAAC;oBACb,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;YAED,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,oCAAoC,CAAC,EAAE,KAAK,CAAC,CAAC;YACtE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF"}