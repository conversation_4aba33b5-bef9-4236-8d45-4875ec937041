import { AIMetadata, ApprovalStatus, AIRule } from './types.js';
export declare class RuleEngine {
    private rules;
    constructor();
    checkBeforeModification(filePath: string, metadata: AIMetadata | null, approvals: ApprovalStatus | null): Promise<{
        allowed: boolean;
        reasons: string[];
        warnings: string[];
    }>;
    getActionsAfterModification(filePath: string, metadata: AIMetadata | null): string[];
    private evaluateRule;
    private evaluateCondition;
    private getDefaultRules;
    addCustomRule(rule: AIRule): void;
    removeRule(ruleId: string): void;
    enableRule(ruleId: string): void;
    disableRule(ruleId: string): void;
    listRules(): AIRule[];
}
//# sourceMappingURL=rule-engine.d.ts.map