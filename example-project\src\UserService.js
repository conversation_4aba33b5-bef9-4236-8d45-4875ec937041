/**
 * @ai-metadata
 * @class: UserService
 * @description: Handles user registration, authentication, and profile management
 * @last-update: 2024-08-21T10:30:00Z
 * @last-editor: <EMAIL>
 * @stability: stable
 * @edit-permissions: method-specific
 * @method-permissions: { "login": "read-only", "register": "allow" }
 * @breaking-changes-risk: high
 * @review-required: true
 * @ai-context: "Core user management - login is critical"
 */

class UserService {
  async login(email, password) {
    // Critical method - read-only
    return await this.db.findUser({ email, password });
  }
}
