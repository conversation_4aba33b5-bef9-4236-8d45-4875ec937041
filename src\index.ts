import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import { MemoryManager } from './memory-manager.js';
import { ChangelogManager } from './changelog-manager.js';
import { MetadataParser } from './metadata-parser.js';
import { RuleEngine } from './rule-engine.js';
import chalk from 'chalk';
import * as path from 'path';

class MCPMemoryServer {
  private server: Server;
  private memoryManager: MemoryManager;
  private changelogManager: ChangelogManager;
  private metadataParser: MetadataParser;
  private ruleEngine: RuleEngine;
  private projectRoot: string;

  constructor() {
    this.projectRoot = process.env.PROJECT_ROOT || process.cwd();
    this.server = new Server({
      name: 'mcp-memory-server',
      version: '1.0.0',
    });

    this.memoryManager = new MemoryManager(this.projectRoot);
    this.changelogManager = new ChangelogManager(this.projectRoot);
    this.metadataParser = new MetadataParser(this.projectRoot);
    this.ruleEngine = new RuleEngine();

    this.setupHandlers();
  }

  private setupHandlers(): void {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        // Memory Management Tools
        {
          name: 'start_session',
          description: 'Start a new AI coding session with a specific task',
          inputSchema: {
            type: 'object',
            properties: {
              task: { type: 'string', description: 'Description of the task to work on' }
            },
            required: ['task']
          }
        },
        {
          name: 'add_session_step',
          description: 'Record completion of a step in the current session',
          inputSchema: {
            type: 'object',
            properties: {
              step: { type: 'string', description: 'Description of the completed step' },
              filesModified: { type: 'array', items: { type: 'string' }, description: 'List of files that were modified' },
              description: { type: 'string', description: 'Optional detailed description' }
            },
            required: ['step', 'filesModified']
          }
        },
        {
          name: 'add_decision',
          description: 'Record an important technical decision',
          inputSchema: {
            type: 'object',
            properties: {
              key: { type: 'string', description: 'Decision key/name' },
              value: { type: 'string', description: 'Decision value' },
              reasoning: { type: 'string', description: 'Reasoning behind the decision' }
            },
            required: ['key', 'value', 'reasoning']
          }
        },
        {
          name: 'get_project_memory',
          description: 'Get current project memory and session state',
          inputSchema: {
            type: 'object',
            properties: {}
          }
        },

        // Approval Management Tools
        {
          name: 'set_file_approval',
          description: 'Set approval status for a file',
          inputSchema: {
            type: 'object',
            properties: {
              filePath: { type: 'string', description: 'Path to the file' },
              approvalType: { type: 'string', enum: ['devApproved', 'codeReviewApproved', 'qaApproved'] },
              approvedBy: { type: 'string', description: 'Who approved it' }
            },
            required: ['filePath', 'approvalType', 'approvedBy']
          }
        },
        {
          name: 'get_file_approval_status',
          description: 'Get approval status for a file',
          inputSchema: {
            type: 'object',
            properties: {
              filePath: { type: 'string', description: 'Path to the file' }
            },
            required: ['filePath']
          }
        },

        // Rule Engine Tools
        {
          name: 'check_before_modification',
          description: 'Check if a file can be modified according to AI metadata rules',
          inputSchema: {
            type: 'object',
            properties: {
              filePath: { type: 'string', description: 'Path to the file to check' }
            },
            required: ['filePath']
          }
        },
        {
          name: 'get_modification_actions',
          description: 'Get actions that should be taken after modifying a file',
          inputSchema: {
            type: 'object',
            properties: {
              filePath: { type: 'string', description: 'Path to the file' }
            },
            required: ['filePath']
          }
        },

        // Metadata Tools
        {
          name: 'parse_file_metadata',
          description: 'Parse AI metadata from a file',
          inputSchema: {
            type: 'object',
            properties: {
              filePath: { type: 'string', description: 'Path to the file' }
            },
            required: ['filePath']
          }
        },
        {
          name: 'update_file_metadata',
          description: 'Update AI metadata in a file',
          inputSchema: {
            type: 'object',
            properties: {
              filePath: { type: 'string', description: 'Path to the file' },
              updates: { type: 'object', description: 'Metadata updates to apply' }
            },
            required: ['filePath', 'updates']
          }
        },
        {
          name: 'find_files_with_metadata',
          description: 'Find all files that contain AI metadata',
          inputSchema: {
            type: 'object',
            properties: {
              pattern: { type: 'string', description: 'File pattern to search (optional)' }
            }
          }
        },

        // Changelog Tools
        {
          name: 'add_changelog_entry',
          description: 'Add an entry to the project changelog',
          inputSchema: {
            type: 'object',
            properties: {
              description: { type: 'string', description: 'Description of the change' },
              filesChanged: { type: 'array', items: { type: 'string' }, description: 'Files that were changed' },
              type: { type: 'string', enum: ['added', 'changed', 'deprecated', 'removed', 'fixed', 'security'] },
              breakingChange: { type: 'boolean', description: 'Whether this is a breaking change' },
              impact: { type: 'string', enum: ['major', 'minor', 'patch'] }
            },
            required: ['description', 'filesChanged', 'type']
          }
        },
        {
          name: 'get_file_changelog',
          description: 'Get changelog entries for a specific file',
          inputSchema: {
            type: 'object',
            properties: {
              filePath: { type: 'string', description: 'Path to the file' }
            },
            required: ['filePath']
          }
        },
        {
          name: 'get_recent_changes',
          description: 'Get recent changelog entries',
          inputSchema: {
            type: 'object',
            properties: {
              days: { type: 'number', description: 'Number of days to look back (default: 7)' }
            }
          }
        }
      ]
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      if (!args) {
        throw new McpError(ErrorCode.InvalidParams, 'Missing arguments');
      }

      try {
        switch (name) {
          // Memory Management
          case 'start_session': {
            const task = args.task as string;
            const sessionId = await this.memoryManager.startNewSession(task);
            return { content: [{ type: 'text', text: `Started session: ${sessionId}` }] };
          }

          case 'add_session_step': {
            const step = args.step as string;
            const filesModified = args.filesModified as string[];
            const description = args.description as string | undefined;
            await this.memoryManager.addSessionStep(step, filesModified, description);
            return { content: [{ type: 'text', text: 'Session step added successfully' }] };
          }

          case 'add_decision': {
            const key = args.key as string;
            const value = args.value as any;
            const reasoning = args.reasoning as string;
            await this.memoryManager.addImportantDecision(key, value, reasoning);
            return { content: [{ type: 'text', text: 'Decision recorded successfully' }] };
          }

          case 'get_project_memory': {
            const projectMemory = await this.memoryManager.getProjectMemory();
            return { content: [{ type: 'text', text: JSON.stringify(projectMemory, null, 2) }] };
          }

          // Approval Management
          case 'set_file_approval': {
            const filePath = args.filePath as string;
            const approvalType = args.approvalType as keyof import('./types').ApprovalStatus;
            const approvedBy = args.approvedBy as string;
            await this.memoryManager.setFileApproval(filePath, approvalType, approvedBy);
            return { content: [{ type: 'text', text: 'File approval set successfully' }] };
          }

          case 'get_file_approval_status': {
            const filePath = args.filePath as string;
            const approval = await this.memoryManager.getFileApprovalStatus(filePath);
            return { content: [{ type: 'text', text: JSON.stringify(approval, null, 2) }] };
          }

          // Rule Engine
          case 'check_before_modification': {
            const filePath = args.filePath as string;
            const metadata = await this.metadataParser.parseFileMetadata(filePath);
            const approvals = await this.memoryManager.getFileApprovalStatus(filePath);
            const checkResult = await this.ruleEngine.checkBeforeModification(filePath, metadata, approvals);
            return { content: [{ type: 'text', text: JSON.stringify(checkResult, null, 2) }] };
          }

          case 'get_modification_actions': {
            const filePath = args.filePath as string;
            const fileMetadata = await this.metadataParser.parseFileMetadata(filePath);
            const actions = this.ruleEngine.getActionsAfterModification(filePath, fileMetadata);
            return { content: [{ type: 'text', text: JSON.stringify(actions, null, 2) }] };
          }

          // Metadata Tools
          case 'parse_file_metadata': {
            const filePath = args.filePath as string;
            const parsedMetadata = await this.metadataParser.parseFileMetadata(filePath);
            return { content: [{ type: 'text', text: JSON.stringify(parsedMetadata, null, 2) }] };
          }

          case 'update_file_metadata': {
            const filePath = args.filePath as string;
            const updates = args.updates as any;
            await this.metadataParser.updateFileMetadata(filePath, updates);
            return { content: [{ type: 'text', text: 'File metadata updated successfully' }] };
          }

          case 'find_files_with_metadata': {
            const pattern = args.pattern as string | undefined;
            const files = await this.metadataParser.findFilesWithMetadata(pattern);
            return { content: [{ type: 'text', text: JSON.stringify(files, null, 2) }] };
          }

          // Changelog Tools
          case 'add_changelog_entry': {
            const currentMemory = await this.memoryManager.getProjectMemory();
            const description = args.description as string;
            const filesChanged = args.filesChanged as string[];
            const type = args.type as 'added' | 'changed' | 'deprecated' | 'removed' | 'fixed' | 'security';
            const breakingChange = (args.breakingChange as boolean) || false;
            const impact = (args.impact as 'major' | 'minor' | 'patch') || 'minor';

            await this.changelogManager.addChangelogEntry({
              sessionId: currentMemory.currentSession.sessionId,
              task: currentMemory.currentSession.task,
              type,
              description,
              filesChanged,
              breakingChange,
              approvals: {},
              impact
            });
            return { content: [{ type: 'text', text: 'Changelog entry added successfully' }] };
          }

          case 'get_file_changelog': {
            const filePath = args.filePath as string;
            const fileChangelog = await this.changelogManager.getChangelogForFile(filePath);
            return { content: [{ type: 'text', text: JSON.stringify(fileChangelog, null, 2) }] };
          }

          case 'get_recent_changes': {
            const days = (args.days as number) || 7;
            const recentChanges = await this.changelogManager.getRecentChanges(days);
            return { content: [{ type: 'text', text: JSON.stringify(recentChanges, null, 2) }] };
          }

          default:
            throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
        }
      } catch (error) {
        console.error(chalk.red(`Error in tool ${name}:`), error);
        throw new McpError(ErrorCode.InternalError, `Tool execution failed: ${error}`);
      }
    });
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.log(chalk.green('🚀 MCP Memory Server is running!'));
    console.log(chalk.blue(`📁 Project root: ${this.projectRoot}`));
  }
}

// Start the server
if (require.main === module) {
  const server = new MCPMemoryServer();
  server.run().catch((error) => {
    console.error(chalk.red('Failed to start server:'), error);
    process.exit(1);
  });
}

export { MCPMemoryServer };
