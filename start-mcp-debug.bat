@echo off
REM MCP Memory Server Debug Launcher for Trae IDE
REM This version shows debug info to help troubleshoot

echo Starting MCP Memory Server Debug Mode...
echo Current Directory: %CD%
echo Node Version:
node --version
echo.

cd /d "D:\Development\Projects\AI\MCP\mcp-memory"
echo Changed to: %CD%

set PROJECT_ROOT=D:\Development\Projects\Products\zendesk-clickup-automation
set NODE_ENV=development
set DEBUG=mcp-memory:*

echo PROJECT_ROOT: %PROJECT_ROOT%
echo NODE_ENV: %NODE_ENV%
echo.

echo Checking if dist/index.js exists...
if exist "dist\index.js" (
    echo ✅ dist/index.js found
) else (
    echo ❌ dist/index.js NOT found - run npm run build first
    pause
    exit /b 1
)

echo.
echo Starting MCP server...
echo (This will appear to hang - that's normal for MCP servers)
echo.

REM Start the MCP server
node dist/index.js
