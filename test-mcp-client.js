#!/usr/bin/env node

/**
 * Simple MCP client to test the memory server
 * This simulates what <PERSON> would do
 */

import { spawn } from 'child_process';
import chalk from 'chalk';

async function testMCPClient() {
  console.log(chalk.blue('🔌 Testing MCP Memory Server with client...'));
  
  // Start the MCP server as a child process
  const serverProcess = spawn('node', ['dist/index.js'], {
    env: {
      ...process.env,
      PROJECT_ROOT: 'D:\\Development\\Projects\\Products\\zendesk-clickup-automation'
    },
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  let responseData = '';
  
  serverProcess.stdout.on('data', (data) => {
    responseData += data.toString();
    console.log(chalk.green('📨 Server response:'), data.toString());
  });
  
  serverProcess.stderr.on('data', (data) => {
    console.log(chalk.red('❌ Server error:'), data.toString());
  });
  
  // Send MCP initialization message
  const initMessage = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  };
  
  console.log(chalk.yellow('📤 Sending initialization...'));
  serverProcess.stdin.write(JSON.stringify(initMessage) + '\n');
  
  // Wait a bit for response
  setTimeout(() => {
    // Send list tools request
    const listToolsMessage = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/list',
      params: {}
    };
    
    console.log(chalk.yellow('📤 Requesting tools list...'));
    serverProcess.stdin.write(JSON.stringify(listToolsMessage) + '\n');
    
    // Wait and then cleanup
    setTimeout(() => {
      console.log(chalk.blue('🏁 Test complete. Cleaning up...'));
      serverProcess.kill();
      
      if (responseData.includes('tools')) {
        console.log(chalk.green('✅ MCP server is working correctly!'));
      } else {
        console.log(chalk.yellow('⚠️  Server started but may need debugging'));
      }
    }, 2000);
    
  }, 1000);
  
  serverProcess.on('close', (code) => {
    console.log(chalk.gray(`Server process exited with code ${code}`));
  });
}

testMCPClient();
