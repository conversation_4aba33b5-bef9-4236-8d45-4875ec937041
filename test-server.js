#!/usr/bin/env node

/**
 * Test script to verify MCP Memory Server is working
 * This bypasses the stdio transport to test the server directly
 */

import { MCPMemoryServer } from './dist/index.js';
import chalk from 'chalk';

async function testServer() {
  console.log(chalk.blue('🧪 Testing MCP Memory Server components...'));
  
  try {
    // Set the same PROJECT_ROOT you used
    process.env.PROJECT_ROOT = 'D:\\Development\\Projects\\Products\\zendesk-clickup-automation';
    
    console.log(chalk.yellow('📁 Project Root:'), process.env.PROJECT_ROOT);
    
    // Create server instance (this tests if it initializes properly)
    const server = new MCPMemoryServer();
    console.log(chalk.green('✅ Server instance created successfully'));
    
    // Test if memory directory gets created
    const memoryDir = `${process.env.PROJECT_ROOT}\\.ai-memory`;
    console.log(chalk.yellow('📂 Memory directory should be:'), memoryDir);
    
    // Test memory manager
    console.log(chalk.blue('🧠 Testing Memory Manager...'));
    
    // Access the private members for testing (normally you wouldn't do this)
    const memoryManager = server.memoryManager;
    
    if (memoryManager) {
      console.log(chalk.green('✅ Memory Manager initialized'));
      
      // Test starting a session
      const sessionId = await memoryManager.startNewSession('Test MCP Server Setup');
      console.log(chalk.green('✅ Session started:'), sessionId);
      
      // Test getting project memory
      const memory = await memoryManager.getProjectMemory();
      console.log(chalk.green('✅ Project memory loaded'));
      console.log(chalk.gray('   Session ID:'), memory.currentSession.sessionId);
      console.log(chalk.gray('   Task:'), memory.currentSession.task);
      
      // Test adding a decision
      await memoryManager.addImportantDecision('test_setup', 'working', 'Server components are functioning correctly');
      console.log(chalk.green('✅ Decision recorded'));
      
    } else {
      console.log(chalk.red('❌ Memory Manager not found'));
    }
    
    console.log(chalk.blue('\n🎉 All components working! Your MCP server is ready.'));
    console.log(chalk.yellow('\n📋 Next step: Configure your AI assistant to connect to this server'));
    
  } catch (error) {
    console.error(chalk.red('❌ Error testing server:'), error.message);
    console.error(chalk.gray('Stack:'), error.stack);
  }
}

testServer();
