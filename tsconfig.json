{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "allowImportingTsExtensions": false, "noEmit": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}